<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <UsingTask TaskName="PreviewFramework.AppBuildTasks.GeneratePreviewAppSettingsTask"
             AssemblyFile="$(PkgPreviewFramework_AppBuildTasks)build/PreviewFramework.AppBuildTasks.dll" />

  <Target Name="GeneratePreviewAppSettings" BeforeTargets="CoreCompile">
    <GeneratePreviewAppSettingsTask
      ProjectPath="$(MSBuildProjectFullPath)"
      OutputPath="$(IntermediateOutputPath)PreviewAppInitializer.g.cs"
      PlatformPreviewApplication="PreviewFramework.App.Maui.MauiPreviewApplication.Instance" />
    <ItemGroup>
      <Compile Include="$(IntermediateOutputPath)PreviewAppInitializer.g.cs" />
    </ItemGroup>
  </Target>
</Project>
