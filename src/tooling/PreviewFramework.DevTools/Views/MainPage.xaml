<Page x:Class="PreviewFramework.DevTools.Presentation.MainPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:viewModels="using:PreviewFramework.DevTools.ViewModels"
      xmlns:converters="using:PreviewFramework.DevTools.Converters"
      xmlns:uen="using:Uno.Extensions.Navigation.UI"
      xmlns:utu="using:Uno.Toolkit.UI"
      NavigationCacheMode="Required">

  <Page.Resources>
    <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
  </Page.Resources>

  <Grid x:Name="PageRoot"
        utu:SafeArea.Insets="VisibleBounds">
    <Grid.RowDefinitions>
      <RowDefinition Height="Auto"/>
      <RowDefinition Height="Auto"/>
      <RowDefinition Height="*"/>
    </Grid.RowDefinitions>

    <!-- Header -->
    <Border Grid.Row="0"
            Background="#FF6B46C1"
            Padding="16,12">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <!-- Title with Logo -->
        <StackPanel Grid.Column="0"
                    Orientation="Horizontal"
                    VerticalAlignment="Center">
          <Border Background="#FF10B981"
                  CornerRadius="4"
                  Width="24"
                  Height="24"
                  Margin="0,0,8,0">
            <TextBlock Text="S"
                       FontWeight="Bold"
                       FontSize="14"
                       Foreground="White"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"/>
          </Border>
          <TextBlock Text="{Binding Title}"
                     FontSize="18"
                     FontWeight="SemiBold"
                     Foreground="White"
                     VerticalAlignment="Center"/>
        </StackPanel>

        <!-- Play Button -->
        <Button Grid.Column="1"
                Background="Transparent"
                BorderThickness="0"
                Margin="0,0,8,0"
                Command="{Binding PlayCommand}">
          <TextBlock Text="▶"
                     FontSize="16"
                     Foreground="White"/>
        </Button>

        <!-- Settings Button -->
        <Button Grid.Column="2"
                Background="Transparent"
                BorderThickness="0"
                Command="{Binding SettingsCommand}">
          <TextBlock Text="⚙"
                     FontSize="16"
                     Foreground="White"/>
        </Button>
      </Grid>
    </Border>

    <!-- Search Bar -->
    <Border Grid.Row="1"
            BorderBrush="#E5E7EB"
            BorderThickness="0,0,0,1"
            Padding="16,12">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <AutoSuggestBox x:Name="SearchBox"
                      Grid.Column="0"
                      Text="{Binding SearchText, Mode=TwoWay}"
                      PlaceholderText="Find components"
                      QueryIcon="Find"
                      Background="Transparent"
                      BorderThickness="0"
                      FontSize="14"
                      Margin="0,0,8,0"/>

        <Button Grid.Column="1"
                Background="Transparent"
                BorderThickness="0"
                Command="{Binding AddComponentCommand}">
          <TextBlock Text="+"
                     FontSize="18"
                     Foreground="#6B7280"/>
        </Button>
      </Grid>
    </Border>

    <!-- Navigation Tree -->
    <TreeView x:Name="NavTree"
            Grid.Row="2"
            CanDragItems="False"
            CanReorderItems="False"
            ItemsSource="{Binding NavTreeItems}">

      <TreeView.ItemTemplate>
        <DataTemplate x:DataType="viewModels:NavTreeItemViewModel">
          <TreeViewItem ItemsSource="{Binding Children}">
            <StackPanel Orientation="Horizontal">
              <!-- Icon -->
              <TextBlock Text="{Binding Icon}"
                        FontSize="14"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"/>

              <!-- Display Name -->
              <TextBlock Text="{Binding DisplayName}"
                        FontSize="14"
                        VerticalAlignment="Center"/>
            </StackPanel>
          </TreeViewItem>
        </DataTemplate>

      </TreeView.ItemTemplate>

    </TreeView>

  </Grid>
</Page>