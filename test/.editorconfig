[*.cs]

# SA1600: Elements should be documented
dotnet_diagnostic.SA1600.severity = silent

# SA1601: Partial elements should be documented
dotnet_diagnostic.SA1601.severity = silent

# SA1602: Enumeration items should be documented
dotnet_diagnostic.SA1602.severity = silent

# SA1615: Element return value should be documented
dotnet_diagnostic.SA1615.severity = silent

# VSTHRD103: Call async methods when in an async method
dotnet_diagnostic.VSTHRD103.severity = silent

# VSTHRD111: Use .ConfigureAwait(bool)
dotnet_diagnostic.VSTHRD111.severity = none

# VSTHRD200: Use Async suffix for async methods
dotnet_diagnostic.VSTHRD200.severity = silent

# CA1014: Mark assemblies with CLSCompliant
dotnet_diagnostic.CA1014.severity = none

# CA1050: Declare types in namespaces
dotnet_diagnostic.CA1050.severity = none

# CA1303: Do not pass literals as localized parameters
dotnet_diagnostic.CA1303.severity = none

# CS1591: Missing XML comment for publicly visible type or member
dotnet_diagnostic.CS1591.severity = silent

# CA1707: Identifiers should not contain underscores
dotnet_diagnostic.CA1707.severity = silent

# CA1062: Validate arguments of public methods
dotnet_diagnostic.CA1062.severity = suggestion

# CA1063: Implement IDisposable Correctly
dotnet_diagnostic.CA1063.severity = silent

# CA1816: Dispose methods should call SuppressFinalize
dotnet_diagnostic.CA1816.severity = silent

# CA2007: Consider calling ConfigureAwait on the awaited task
dotnet_diagnostic.CA2007.severity = none

# SA1401: Fields should be private
dotnet_diagnostic.SA1401.severity = silent

# SA1133: Do not combine attributes
dotnet_diagnostic.SA1133.severity = silent
