<?xml version="1.0" encoding="utf-8"?>
<!-- Use a separate, simpler, Directory.Build.props file for samples -->
<Project>
    <PropertyGroup>
        <RepoRootPath>$(MSBuildThisFileDirectory)..\</RepoRootPath>
        <BaseIntermediateOutputPath>$(RepoRootPath)obj\$([MSBuild]::MakeRelative($(RepoRootPath), $(MSBuildProjectDirectory)))\</BaseIntermediateOutputPath>
        <BaseOutputPath Condition=" '$(BaseOutputPath)' == '' ">$(RepoRootPath)bin\$(MSBuildProjectName)\</BaseOutputPath>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>

        <!-- Suppress the following warnings:
          CS8002: Referenced assembly does not have a strong name -->
        <NoWarn>$(NoWarn);CS8002</NoWarn>
    </PropertyGroup>
</Project>
