<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android;net9.0-ios</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net6.0-tizen</TargetFrameworks> -->
		<OutputType>Exe</OutputType>
		<RootNamespace>EcommerceMAUI</RootNamespace>
		<UseMaui>true</UseMaui>
		<SkipValidateMauiImplicitPackageReferences>true</SkipValidateMauiImplicitPackageReferences>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<WindowsPackageType>None</WindowsPackageType>

		<!-- Display name -->
		<ApplicationTitle>EcommerceMAUI</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.ecommercemaui</ApplicationId>
		<ApplicationIdGuid>08E8D34A-0937-4149-A280-A7B524BC7913</ApplicationIdGuid>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
	</PropertyGroup>

  <PropertyGroup>
    <!-- Suppress the following warnings:
		  CS8618: Non-nullable property is uninitialized.
		  CS8625: Cannot convert null literal to non-nullable reference type.
		  CS8767: Nullability of reference types in value of type 'T' doesn't match target type 'T?'.
		  CS8602: Dereference of a possibly null reference.
		  CS0618: Type or member is obsolete.
		  CS1998: This async method lacks 'await' operators and will run synchronously.
		  CS8612: Nullability of reference types in value of type 'T' doesn't match target type 'T?'.
		  CS0168: The variable 'x' is declared but never used.
		  CS8603: Possible null reference return.
		  CS4014: Because this call is not awaited, execution of the current method continues before the call is completed.
		  CS8600: Converting null literal or possible null value to non-nullable type.
		  XC0022: Binding could be compiled to improve runtime performance if x:DataType is specified.
		  XC0025: Binding was not compiled because it has an explicitly set Source property and compilation of bindings with Source is not enabled. -->
    <NoWarn>$(NoWarn);CS8618;CS8625;CS8767;CS8602;CS0618;CS1998;CS8612;CS0168;CS8603;CS4014;CS8600;XC0022;XC0025</NoWarn>
  </PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#00C569" Resize="false" BaseSize="128,128" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#00C569" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.svg" BaseSize="168,208" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="Resources\Fonts\Material-Icon.ttf" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Views\AllProductView.xaml.cs">
	    <DependentUpon>AllProductView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\BrandDetailView.xaml.cs">
	    <DependentUpon>BrandDetailView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\ConfirmAddressView.xaml.cs">
	    <DependentUpon>ConfirmAddressView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\ScanCameraView.xaml.cs">
	    <DependentUpon>ScanCameraView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\CartView.xaml.cs">
	    <DependentUpon>CartView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\CategoryDetailView.xaml.cs">
	    <DependentUpon>CategoryDetailView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\HomePageView.xaml.cs">
	    <DependentUpon>HomePageView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\OrderDetailsView.xaml.cs">
	    <DependentUpon>OrderDetailsView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\ProductDetailsView.xaml.cs">
	    <DependentUpon>ProductDetailsView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\ProfileView.xaml.cs">
	    <DependentUpon>ProfileView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\RegisterView.xaml.cs">
	    <DependentUpon>RegisterView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\TrackOrderView.xaml.cs">
	    <DependentUpon>TrackOrderView.xaml</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <MauiXaml Update="Controls\CreditCardView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\AddNewCardView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\AllProductView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\BrandDetailView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\CartCalculation.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\ConfirmAddressView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\ConfirmPaymentView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\DeliveryTypeView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\FinishCartView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\ScanCameraView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\CardView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\CartView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\CategoryDetailView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\HomePageView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\LoginView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\OrderDetailsView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\ProductDetailsView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\ProfileView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\RegisterView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\ShippingAddressView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\TrackOrderView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\VerificationView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\WishListView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Update="Microsoft.Maui.Controls" Version="8.0.80" />
	  <PackageReference Update="Microsoft.Maui.Controls.Compatibility" Version="8.0.80" />
	  <PackageReference Include="CommunityToolkit.Maui" Version="9.0.2" />
	  <PackageReference Include="PINView.MAUI" Version="1.0.2" />
	  <PackageReference Include="Camera.MAUI" Version="1.5.1" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\..\src\platforms\PreviewFramework.App.Maui\PreviewFramework.App.Maui.csproj" />
	</ItemGroup>

  <!-- Since we using a project reference instead of referencing the NuGet we need to also import the props -->
  <Import Project="..\..\..\src\PreviewFramework\buildTransitive\PreviewFramework.props" />

  <UsingTask TaskName="PreviewFramework.AppBuildTasks.GeneratePreviewAppSettingsTask" AssemblyFile="Q:\src\ui-preview-framework\bin\PreviewFramework.AppBuildTasks\Debug\netstandard2.0\PreviewFramework.AppBuildTasks.dll" />
  <Import Project="..\..\..\src\platforms\PreviewFramework.App.Maui\build\PreviewFramework.App.Maui.targets" />

  <ProjectExtensions><VisualStudio><UserProperties XamarinHotReloadDebuggerTimeoutExceptionEcommerceMAUIHideInfoBar="True" /></VisualStudio></ProjectExtensions>

</Project>
